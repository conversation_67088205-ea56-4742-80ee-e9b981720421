package io.vault.jasper.repository.activity

import io.vault.jasper.model.activity.CampaignRanking
import io.vault.jasper.model.activity.CampaignRankingType
import org.springframework.data.mongodb.repository.MongoRepository
import org.springframework.stereotype.Repository
import java.util.Optional

@Repository
interface CampaignRankingRepository : MongoRepository<CampaignRanking, String> {
    fun findByCampaignId(campaignId: String): Optional<CampaignRanking>
    
    fun findByCampaignIdAndEnabled(campaignId: String, enabled: Boolean): Optional<CampaignRanking>
    
    fun findByRankingType(rankingType: CampaignRankingType): List<CampaignRanking>
    
    fun findByEnabledTrue(): List<CampaignRanking>
}
