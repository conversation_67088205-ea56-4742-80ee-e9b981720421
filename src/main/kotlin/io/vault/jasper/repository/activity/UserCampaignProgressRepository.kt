package io.vault.jasper.repository.activity

import io.vault.jasper.model.activity.UserCampaignProgress
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.mongodb.repository.MongoRepository
import org.springframework.stereotype.Repository
import java.math.BigDecimal
import java.util.Optional

@Repository
interface UserCampaignProgressRepository : MongoRepository<UserCampaignProgress, String> {
    fun findByUserIdAndCampaignId(userId: String, campaignId: String): Optional<UserCampaignProgress>

    fun findByUserId(userId: String): List<UserCampaignProgress>

    fun findByCampaignId(campaignId: String): List<UserCampaignProgress>

    fun findByCampaignId(campaignId: String, pageable: Pageable): Page<UserCampaignProgress>

    fun countByCampaignId(campaignId: String): Long

    fun findByCampaignIdOrderByOptionTradeCountDesc(campaignId: String, pageable: Pageable): Page<UserCampaignProgress>

    fun findByCampaignIdOrderByOptionTradeAmountDesc(campaignId: String, pageable: Pageable): Page<UserCampaignProgress>

    fun findByCampaignIdOrderByOptionTradePremiumDesc(campaignId: String, pageable: Pageable): Page<UserCampaignProgress>

    fun countByCampaignIdAndOptionTradeCountGreaterThan(campaignId: String, optionTradeCount: Int): Long

    fun countByCampaignIdAndOptionTradeAmountGreaterThan(campaignId: String, optionTradeAmount: BigDecimal): Long

    fun countByCampaignIdAndOptionTradePremiumGreaterThan(campaignId: String, optionTradePremium: BigDecimal): Long
}
