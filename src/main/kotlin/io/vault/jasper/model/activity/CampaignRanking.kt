package io.vault.jasper.model.activity

import org.springframework.data.annotation.CreatedDate
import org.springframework.data.annotation.Id
import org.springframework.data.annotation.LastModifiedDate
import org.springframework.data.mongodb.core.index.Indexed
import org.springframework.data.mongodb.core.mapping.Document
import org.springframework.data.mongodb.core.mapping.Field
import java.time.LocalDateTime

/**
 * CampaignRanking model to configure ranking logic for campaigns
 */
@Document("campaign_rankings")
data class CampaignRanking(
    @Id
    val id: String? = null,

    @Field("campaign_id")
    @Indexed(background = true, unique = true)
    val campaignId: String,

    @Field("ranking_type")
    val rankingType: CampaignRankingType,

    @Field("ranking_config")
    val rankingConfig: Map<String, Any> = mapOf(),

    @Field("display_config")
    val displayConfig: CampaignRankingDisplayConfig,

    @Field("enabled")
    var enabled: Boolean = true,

    @CreatedDate
    val created: LocalDateTime = LocalDateTime.now(),

    @LastModifiedDate
    val updated: LocalDateTime = LocalDateTime.now(),

    @Field("i18n_info")
    var i18nInfo: MutableMap<String, I18nCampaignRankingInfo>? = null
)

/**
 * Enum representing different types of campaign ranking
 */
enum class CampaignRankingType {
    OPTION_TRADE_COUNT,
    OPTION_TRADE_AMOUNT,
    OPTION_TRADE_PREMIUM,
    COMPLETED_TASK_COUNT,
    CUSTOM_SCORE
}

/**
 * Configuration for ranking display
 */
data class CampaignRankingDisplayConfig(
    @Field("show_rank")
    val showRank: Boolean = true,

    @Field("show_address")
    val showAddress: Boolean = true,

    @Field("show_value")
    val showValue: Boolean = true,

    @Field("show_percentage")
    val showPercentage: Boolean = false,

    @Field("address_display_format")
    val addressDisplayFormat: AddressDisplayFormat = AddressDisplayFormat.MASKED,

    @Field("value_display_format")
    val valueDisplayFormat: ValueDisplayFormat = ValueDisplayFormat.FULL,

    @Field("decimal_places")
    val decimalPlaces: Int = 2
)

/**
 * Enum for address display format
 */
enum class AddressDisplayFormat {
    FULL,
    MASKED,
    FIRST_LAST_4
}

/**
 * Enum for value display format
 */
enum class ValueDisplayFormat {
    FULL,
    ABBREVIATED,
    PERCENTAGE
}

/**
 * I18n information for CampaignRanking
 */
data class I18nCampaignRankingInfo(
    val title: String,
    val description: String,
    val valueLabel: String,
    val noDataMessage: String = "No data available"
)

/**
 * Constants for ranking configuration parameters
 */
object CampaignRankingConfigParams {
    // CUSTOM_SCORE
    const val SCORE_FORMULA = "scoreFormula"
    const val WEIGHT_TRADE_COUNT = "weightTradeCount"
    const val WEIGHT_TRADE_AMOUNT = "weightTradeAmount"
    const val WEIGHT_TRADE_PREMIUM = "weightTradePremium"
    const val WEIGHT_COMPLETED_TASKS = "weightCompletedTasks"
    
    // General
    const val MIN_TRADE_COUNT = "minTradeCount"
    const val MIN_TRADE_AMOUNT = "minTradeAmount"
    const val EXCLUDE_ADDRESSES = "excludeAddresses"
}
