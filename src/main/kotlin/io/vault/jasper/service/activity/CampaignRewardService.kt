package io.vault.jasper.service.activity

import io.vault.jasper.enums.Symbol
import io.vault.jasper.exception.BusinessException
import io.vault.jasper.exception.ResultEnum
import io.vault.jasper.model.OptionOrder
import io.vault.jasper.model.OptionStatus
import io.vault.jasper.model.User
import io.vault.jasper.model.activity.*
import io.vault.jasper.repository.activity.CampaignRewardRecordRepository
import io.vault.jasper.repository.activity.CampaignRewardRepository
import io.vault.jasper.service.CurrencyService
import io.vault.jasper.service.OptionOrderService
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import java.math.BigDecimal
import java.time.LocalDateTime

@Service
class CampaignRewardService(
    private val campaignService: CampaignService,
    private val campaignRewardRepository: CampaignRewardRepository,
    private val campaignRewardRecordRepository: CampaignRewardRecordRepository,
    private val currencyService: CurrencyService,
    private val optionOrderService: OptionOrderService
) {
    private val logger = LoggerFactory.getLogger(this::class.java)
    
    /**
     * Create a new campaign reward
     */
    //@Transactional
    fun createCampaignReward(campaignReward: CampaignReward): CampaignReward {
        // Validate that the activity exists
        val campaign = campaignService.getCampaignById(campaignReward.campaignId)
            ?: throw BusinessException(ResultEnum.CAMPAIGN_NOT_FOUND)
        
        // Check if a reward of this type already exists for the activity
        campaignRewardRepository.findByCampaignIdAndRewardType(campaignReward.campaignId, campaignReward.rewardType)
            .ifPresent {
                throw BusinessException(ResultEnum.REWARD_TYPE_ALREADY_EXIST)
            }
        
        return campaignRewardRepository.save(campaignReward)
    }
    
    /**
     * Get rewards for a campaign
     */
    fun getRewardsForCampaign(campaignId: String): List<CampaignReward> {
        return campaignRewardRepository.findByCampaignId(campaignId)
    }
    
    /**
     * Process an option order for rewards
     */
    //@Transactional
    fun processOptionOrderForReward(user: User, optionOrder: OptionOrder) {
        // Only process buyer profit is not null
        if (optionOrder.buyerProfit == null) {
            logger.info("CampaignRewardService Option order ${optionOrder.id} buyer profit is null, skipping")
            return
        }
        
        // Get all active campaigns
        val campaigns = campaignService.getActiveCampaigns()
        
        for (campaign in campaigns) {
            // Check if the user has completed any tasks in this campaign
            val progress = campaignService.getUserCampaignProgress(user.id!!, user.address,campaign.id!!)

            // Update user campaign progress with option trade statistics
            campaignService.updateUserCampaignProgressWithOptionTrade(user, optionOrder)

            // If the user hasn't completed any tasks, skip this campaign
            if (progress.completedTaskIds.isEmpty()) {
                continue
            }

            //Check if the existing reward record count exceed the max count of campaign
            val existingRecordCount = campaignRewardRecordRepository.countByCampaignId(
                campaign.id
            )
            if (existingRecordCount >= campaign.totalRewardAmount.toLong()) {
                logger.info("CampaignRewardService Campaign ${campaign.id} reach max reward count, $existingRecordCount, ${campaign.totalRewardAmount}")
                continue
            }
            
            // Get the order loss compensation reward for this campaign
            val orderLossReward = campaignRewardRepository.findByCampaignIdAndRewardType(
                campaign.id,
                RewardType.ORDER_LOSS_COMPENSATION
            ).orElse(null) ?: continue
            
            // Check if a reward record already exists for this order
            val existingRecord = campaignRewardRecordRepository.findByUserIdAndCampaignIdAndOptionOrderId(
                user.id,
                campaign.id,
                optionOrder.id!!
            ).orElse(null)
            
            if (existingRecord != null) {
                continue
            }

            val netProfit = optionOrderService.calculateNetProfit(optionOrder)

            // 转化为可读的数值
            val usdtDecimal = currencyService.getCurrencyDecimal(optionOrder.chain, Symbol.USDT)
            val compensation = netProfit.multiply(BigDecimal("-1")).movePointLeft(usdtDecimal).setScale(6, BigDecimal.ROUND_HALF_UP)

            val genRewardRecordForZeroReward = (orderLossReward.rewardConfig[RewardConfigParams.GEN_REWARD_RECORD_FOR_ZERO_REWARD] == "true")

            if (!genRewardRecordForZeroReward && compensation.compareTo(BigDecimal.ZERO) != 1) {
                continue
            }

            var status = RewardRecordStatus.PENDING
            if(compensation.compareTo(BigDecimal.ZERO) != 1){
                status = RewardRecordStatus.COMPLETED
            }

            // Create a reward record
            val rewardRecord = CampaignRewardRecord(
                userId = user.id,
                address = user.address,
                campaignId = campaign.id,
                rewardType = RewardType.ORDER_LOSS_COMPENSATION,
                optionOrderId = optionOrder.id,
                chain = optionOrder.chain,
                amount = compensation,
                status = status,
                scheduledTime = LocalDateTime.now().plusMinutes(1) // Schedule for 5 minutes later
            )

            campaignRewardRecordRepository.save(rewardRecord)

            // Update the distributed amount
            orderLossReward.distributedAmount = orderLossReward.distributedAmount.add(compensation)
            campaignRewardRepository.save(orderLossReward)
        }
    }
    
    /**
     * Process pending reward records
     * This method is scheduled to run every minute
     */
    //@Scheduled(fixedRate = 60000)
    //@Transactional
    fun processPendingRewardRecords() {
        val now = LocalDateTime.now()
        val pendingRecords = campaignRewardRecordRepository.findByStatusAndScheduledTimeBefore(
            RewardRecordStatus.PENDING,
            now
        )
        
        for (record in pendingRecords) {
            try {
                // Update the status to processing
                record.status = RewardRecordStatus.PROCESSING
                campaignRewardRecordRepository.save(record)
                
                // TODO: Submit the compensation to the smart contract
                // This would involve calling a Web3 service to interact with the contract
                
                // For now, just mark it as completed
                record.status = RewardRecordStatus.COMPLETED
                record.transactionHash = "mock_tx_hash" // Replace with actual transaction hash
                campaignRewardRecordRepository.save(record)
            } catch (e: Exception) {
                logger.error("Error processing reward record ${record.id}", e)
                
                // Mark as failed
                record.status = RewardRecordStatus.FAILED
                record.errorMessage = e.message
                campaignRewardRecordRepository.save(record)
            }
        }
    }
    
    /**
     * Get reward records for a user in a campaign
     */
    fun getRewardRecordsForUserInCampaign(userId: String, campaignId: String): List<CampaignRewardRecord> {
        return campaignRewardRecordRepository.findByUserIdAndCampaignId(userId, campaignId)
    }
    
    /**
     * Get total rewards distributed for a campaign
     */
    fun getTotalRewardsDistributedForCampaign(campaignId: String): BigDecimal {
        val rewards = campaignRewardRepository.findByCampaignId(campaignId)
        return rewards.sumOf { it.distributedAmount }
    }
}
