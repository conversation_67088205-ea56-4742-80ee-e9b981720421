package io.vault.jasper.response

import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty
import io.vault.jasper.enums.ChainType
import io.vault.jasper.model.activity.I18nCampaignInfo
import io.vault.jasper.model.activity.I18nCampaignRewardInfo
import io.vault.jasper.model.activity.I18nTaskGroupInfo
import io.vault.jasper.model.activity.I18nTaskInfo
import io.vault.jasper.model.activity.RewardRecordStatus
import io.vault.jasper.model.activity.RewardType
import io.vault.jasper.model.activity.TaskType
import java.math.BigDecimal
import java.time.LocalDateTime

/**
 * Response class for campaign information
 */
@ApiModel
data class CampaignResponse(
    @ApiModelProperty("Campaign ID", required = true)
    val id: String,

    @ApiModelProperty("Campaign name", required = true)
    val name: String,

    @ApiModelProperty("Campaign chain", required = true)
    val chain: ChainType,

    @ApiModelProperty("Campaign start time", required = true)
    val startTime: LocalDateTime,

    @ApiModelProperty("Campaign end time", required = true)
    val endTime: LocalDateTime,

    @ApiModelProperty("Total reward amount", required = true)
    val totalRewardAmount: String,

    @ApiModelProperty("Participant count", required = true)
    val participantCount: Int,

    @ApiModelProperty("Completed count", required = true)
    val completedCount: Int,

    @ApiModelProperty("Task Groups in the Campaign", required = true)
    val taskGroups: List<TaskGroupResponse>,

    @ApiModelProperty("Campaign Rewards", required = true)
    val campaignRewards: List<CampaignRewardResponse>,

    @ApiModelProperty("User progress", required = true)
    val userProgress: UserProgressResponse,

    @ApiModelProperty("User rewards", required = true)
    val userRewards: List<RewardResponse>,

    @ApiModelProperty("i18n info", required = false)
    val i18nInfo: Map<String, I18nCampaignInfo>? = null
)

/**
 * Response class for task information
 */
@ApiModel
data class TaskResponse(
    @ApiModelProperty("Task ID", required = true)
    val id: String,

    @ApiModelProperty("Task title", required = true)
    val title: String,

    @ApiModelProperty("Task type", required = true)
    val taskType: TaskType,

    @ApiModelProperty("Task configuration", required = true)
    val taskConfig: Map<String, Any>,

    @ApiModelProperty("Whether the task is completed by the user", required = true)
    val completed: Boolean,

    @ApiModelProperty("When the task was completed by the user", required = false)
    val completedAt: LocalDateTime?,

    @ApiModelProperty("i18n info", required = false)
    val i18nInfo: Map<String, I18nTaskInfo>? = null
)

/**
 * Response class for user progress information
 */
@ApiModel
data class UserProgressResponse(
    @ApiModelProperty("Number of completed tasks", required = true)
    val completedTaskCount: Int,

    @ApiModelProperty("Total number of tasks", required = true)
    val totalTaskCount: Int,

    @ApiModelProperty("Whether all rewards have been claimed", required = true)
    val rewardsClaimed: Boolean,

    @ApiModelProperty("Option trade count", required = true)
    val optionTradeCount: Int,

    @ApiModelProperty("Option trade amount", required = true)
    val optionTradeAmount: String,

    @ApiModelProperty("Option trade premium", required = true)
    val optionTradePremium: String,

    @ApiModelProperty("Extra information", required = false)
    val extraInfo: Map<String, Any> = mapOf()
)

/**
 * Response class for reward information
 */
@ApiModel
data class RewardResponse(
    @ApiModelProperty("Reward ID", required = true)
    val id: String,

    @ApiModelProperty("Reward type", required = true)
    val rewardType: RewardType,

    @ApiModelProperty("Reward amount", required = true)
    val amount: BigDecimal,

    @ApiModelProperty("Reward status", required = true)
    val status: RewardRecordStatus,

    @ApiModelProperty("Transaction hash", required = false)
    val transactionHash: String?,

    @ApiModelProperty("When the reward was created", required = true)
    val created: LocalDateTime,

    @ApiModelProperty("When the reward was last updated", required = true)
    val updated: LocalDateTime
)

/**
 * Response class for task group information
 */
@ApiModel
data class TaskGroupResponse(
    @ApiModelProperty("Task Group ID", required = true)
    val id: String,

    @ApiModelProperty("Task Group title", required = true)
    val title: String,

    @ApiModelProperty("Tasks in the Task Group", required = true)
    val taskIds: List<String>,

    @ApiModelProperty("Tasks in the Task Group", required = true)
    val tasks: List<TaskResponse>,

    @ApiModelProperty("Whether all the tasks in group are completed by the user", required = true)
    val completed: Boolean,

    @ApiModelProperty("i18n info", required = false)
    val i18nInfo: Map<String, I18nTaskGroupInfo>? = null
)

/**
 * Response class for campaign reward information
 */
@ApiModel
data class CampaignRewardResponse(
    @ApiModelProperty("Reward ID", required = true)
    val id: String,

    @ApiModelProperty("Campaign ID", required = true)
    val campaignId: String,

    @ApiModelProperty("Reward type", required = true)
    val rewardType: RewardType,

    @ApiModelProperty("Reward configuration", required = true)
    val rewardConfig: Map<String, Any>,

    @ApiModelProperty("Total reward amount", required = true)
    val totalRewardAmount: BigDecimal,

    @ApiModelProperty("Distributed amount", required = true)
    val distributedAmount: BigDecimal,

    @ApiModelProperty("i18n info", required = false)
    val i18nInfo: Map<String, I18nCampaignRewardInfo>? = null
)
