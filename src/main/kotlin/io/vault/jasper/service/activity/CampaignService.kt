package io.vault.jasper.service.activity

import io.vault.jasper.exception.BusinessException
import io.vault.jasper.exception.ResultEnum
import io.vault.jasper.model.OptionOrder
import io.vault.jasper.model.OptionStatus
import io.vault.jasper.model.User
import io.vault.jasper.model.activity.*
import io.vault.jasper.repository.OptionOrderRepository
import io.vault.jasper.repository.activity.*
import io.vault.jasper.service.activity.processors.TaskProcessorFactory
import org.slf4j.LoggerFactory
import org.springframework.data.repository.findByIdOrNull
import org.springframework.stereotype.Service
import java.math.BigDecimal
import java.time.LocalDateTime

@Service
class CampaignService(
    private val campaignRepository: CampaignRepository,
    private val taskRepository: TaskRepository,
    private val userCampaignProgressRepository: UserCampaignProgressRepository,
    private val campaignRewardRepository: CampaignRewardRepository,
    private val taskProcessorFactory: TaskProcessorFactory,
    private val taskGroupRepository: TaskGroupRepository,
    private val optionOrderRepository: OptionOrderRepository
) {
    private val logger = LoggerFactory.getLogger(this::class.java)

    /**
     * Create a new campaign
     */
    //@Transactional
    fun createCampaign(campaign: Campaign): Campaign {
        // Validate that all task IDs exist
        if (campaign.taskIds.isNotEmpty()) {
            val tasks = taskRepository.findByIdIn(campaign.taskIds)
            if (tasks.size != campaign.taskIds.size) {
                throw BusinessException(ResultEnum.TASK_ID_NOT_EXIST)
            }
        }

        // Validate that all task group IDs exist
        if (campaign.taskGroupIds.isNotEmpty()) {
            val taskGroups = taskGroupRepository.findByIdIn(campaign.taskGroupIds)
            if (taskGroups.size != campaign.taskGroupIds.size) {
                throw BusinessException(ResultEnum.TASK_GROUP_NOT_FOUND)
            }
        }

        // Check if an activity with the same name already exists
        campaignRepository.findByName(campaign.name).ifPresent {
            throw BusinessException(ResultEnum.CAMPAIGN_NOT_FOUND)
        }

        return campaignRepository.save(campaign)
    }

    /**
     * Get a campaign by ID
     */
    fun getCampaignById(id: String): Campaign? {
        return campaignRepository.findByIdOrNull(id)
    }

    /**
     * Get all active campaigns
     */
    fun getActiveCampaigns(): List<Campaign> {
        val currentTime = LocalDateTime.now()
        return campaignRepository.findByStartTimeLessThanEqualAndEndTimeGreaterThanEqual(
            currentTime,
            currentTime
        ).filter { it.active }
    }

    /**
     * Get tasks for a campaign
     */
    fun getTasksForCampaign(campaignId: String): List<Task> {
        val campaign = getCampaignById(campaignId)
            ?: throw BusinessException(ResultEnum.CAMPAIGN_NOT_FOUND)

        // Get tasks directly associated with the campaign
        val directTasks = taskRepository.findByIdIn(campaign.taskIds)

        // Get tasks from task groups
        val taskGroupTasks = mutableListOf<Task>()
        if (campaign.taskGroupIds.isNotEmpty()) {
            // Get all task groups
            val taskGroups = taskGroupRepository.findByIdIn(campaign.taskGroupIds)

            // Get all task IDs from task groups
            val taskIds = taskGroups.flatMap { it.taskIds }

            // Get all tasks
            val tasks = taskRepository.findByIdIn(taskIds)

            taskGroupTasks.addAll(tasks)
        }

        // Combine direct tasks and task group tasks, removing duplicates
        return (directTasks + taskGroupTasks).distinctBy { it.id }
    }

    /**
     * Get task groups for a campaign
     */
    fun getTaskGroupsForCampaign(campaignId: String): List<TaskGroup> {
        val campaign = getCampaignById(campaignId)
            ?: throw BusinessException(ResultEnum.CAMPAIGN_NOT_FOUND)

        if (campaign.taskGroupIds.isEmpty()) {
            return listOf()
        }

        // Get all task groups for this campaign
        return taskGroupRepository.findByIdIn(campaign.taskGroupIds)
    }

    /**
     * Get user progress for a campaign
     */
    fun getUserCampaignProgress(
        userId: String,
        address: String,
        campaignId: String
    ): UserCampaignProgress {
        return userCampaignProgressRepository.findByUserIdAndCampaignId(userId, campaignId)
            .orElseGet {
                UserCampaignProgress(
                    userId = userId,
                    address = address,
                    campaignId = campaignId
                )
            }
    }

    /**
     * Check if a task is completed for a user
     */
    fun isTaskCompleted(user: User, task: Task, context: Map<String, Any> = emptyMap()): Pair<Boolean, Map<String, Any>> {
        val processor = taskProcessorFactory.getProcessor(task.taskType)
        return processor.isTaskCompleted(user, task, context)
    }

    /**
     * Mark a task as completed for a user
     */
    //@Transactional
    fun completeTask(
        user: User,
        campaignId: String,
        taskId: String,
        context: Map<String, Any> = emptyMap()
    ): UserCampaignProgress {
        val campaign = getCampaignById(campaignId)
            ?: throw BusinessException(ResultEnum.CAMPAIGN_NOT_FOUND)

        if (!campaign.active || LocalDateTime.now().isBefore(campaign.startTime) || LocalDateTime.now().isAfter(campaign.endTime)) {
            throw BusinessException(ResultEnum.CAMPAIGN_NOT_ACTIVE)
        }

        // Check if the task is directly in the campaign or in any of the campaign's task groups
        val isTaskInCampaign = campaign.taskIds.contains(taskId)

        if (!isTaskInCampaign && campaign.taskGroupIds.isNotEmpty()) {
            // Check if the task is in any of the campaign's task groups
            val taskGroups = taskGroupRepository.findByIdIn(campaign.taskGroupIds)
            val isTaskInTaskGroups = taskGroups.any { it.taskIds.contains(taskId) }

            if (!isTaskInTaskGroups) {
                throw BusinessException(ResultEnum.TASK_NOT_PART_OF_CAMPAIGN)
            }
        } else if (!isTaskInCampaign) {
            throw BusinessException(ResultEnum.TASK_NOT_PART_OF_CAMPAIGN)
        }

        val task = taskRepository.findByIdOrNull(taskId)
            ?: throw BusinessException(ResultEnum.TASK_NOT_FOUND)

        // Check if the task is completed
        val (isCompleted, completionData) = isTaskCompleted(user, task, context + ("campaignId" to campaignId))

        if (!isCompleted) {
            throw BusinessException(ResultEnum.TASK_NOT_COMPLETED)
        }

        // Get or create user campaign progress
        val progress = getUserCampaignProgress(user.id!!, user.address,campaignId)

        // If the task is already completed, just return the progress
        if (progress.completedTaskIds.contains(taskId)) {
            return progress
        }

        // Mark the task as completed
        progress.completedTaskIds.add(taskId)
        progress.taskCompletionData[taskId] = TaskCompletionData(
            completedAt = LocalDateTime.now(),
            completionData = completionData
        )

        // Save the progress
        val savedProgress = userCampaignProgressRepository.save(progress)

        // Update participant count if this is the first task completed by this user
        if (progress.completedTaskIds.size == 1) {
            campaign.participantCount++
            campaignRepository.save(campaign)
        }

        return savedProgress
    }

    fun processUserIncompletedTasksInCampaign(
        user: User,
        campaign: Campaign,
        userProgress: UserCampaignProgress
    ) {

        // Get all tasks for the campaign
        val tasks = getTasksForCampaign(campaign.id!!)

        // Filter for bind tasks
        val incompletedTasks = tasks.filter {
            !userProgress.completedTaskIds.contains(it.id)
        }

        val context = mapOf(
            "campaignId" to campaign.id
        )

        for (task in incompletedTasks) {
            try {
                // Check if the task is completed
                val (isCompleted, _) = isTaskCompleted(user, task, context)

                if (isCompleted) {
                    // Mark the task as completed
                    completeTask(user, campaign.id, task.id!!, context)
                    userProgress.completedTaskIds.add(task.id)
                }
            } catch (e: Exception) {
                logger.error("Error processing Incompleted task ${task.id} ${task.taskType} in campaign ${campaign.id}, ${campaign.name}", e)
            }
        }
    }

    /**
     * Process an option order for campaign tasks
     */
    //@Transactional
    fun processOptionOrder(user: User, optionOrder: OptionOrder) {
        // Get all active campaigns
        val campaigns = getActiveCampaigns()

        for (campaign in campaigns) {
            // Get all tasks for the campaign
            val tasks = getTasksForCampaign(campaign.id!!)

            // Filter for option order completion tasks
            val optionOrderTasks = tasks.filter { it.taskType == TaskType.OPTION_ORDER_COMPLETION }

            for (task in optionOrderTasks) {
                try {
                    // Check if the task is completed with this order
                    val context = mapOf(
                        "optionOrder" to optionOrder,
                        "campaignId" to campaign.id
                    )

                    val (isCompleted, _) = isTaskCompleted(user, task, context)

                    if (isCompleted) {
                        // Mark the task as completed
                        completeTask(user, campaign.id, task.id!!, context)
                    }
                } catch (e: Exception) {
                    logger.error("Error processing option order for task ${task.id} in campaign ${campaign.id}", e)
                }
            }
        }
    }

    /**
     * Get all campaigns for a user with their progress
     */
    fun getCampaignsForUser(user: User): List<Pair<Campaign, UserCampaignProgress>> {
        val campaigns = getActiveCampaigns()

        return campaigns.map { campaign ->
            val progress = getUserCampaignProgress(user.id!!, user.address, campaign.id!!)
            Pair(campaign, progress)
        }
    }

    /**
     * Get the number of participants for an activity
     */
    fun getParticipantCount(campaignId: String): Long {
        return userCampaignProgressRepository.countByCampaignId(campaignId)
    }

    /**
     * Update user campaign progress with option trade statistics
     */
    fun updateUserCampaignProgressWithOptionTrade(
        user: User,
        optionOrder: OptionOrder
    ) {
        // Get all active campaigns
        val campaigns = getActiveCampaigns()

        for (campaign in campaigns) {
            // Check if the option order is within the campaign period
            if (optionOrder.created.isBefore(campaign.startTime) || optionOrder.created.isAfter(campaign.endTime)) {
                continue
            }

            // Get or create user campaign progress
            val progress = getUserCampaignProgress(user.id!!, user.address, campaign.id!!)

            // Update option trade statistics
            progress.optionTradeCount++

            // Add trade amount (using bidAmount or amount)
            val tradeAmount = optionOrder.bidAmount ?: optionOrder.amount
            progress.optionTradeAmount = progress.optionTradeAmount.add(tradeAmount)

            // Add premium amount (using premiumFeeShouldPay)
            optionOrder.premiumFeeShouldPay?.let { premium ->
                progress.optionTradePremium = progress.optionTradePremium.add(premium)
            }

            // Save the updated progress
            userCampaignProgressRepository.save(progress)
        }
    }

    /**
     * Calculate option trade statistics for a user in a campaign
     */
    fun calculateOptionTradeStatistics(
        userId: String,
        campaignId: String
    ): Triple<Int, BigDecimal, BigDecimal> {
        val campaign = getCampaignById(campaignId) ?: return Triple(0, BigDecimal.ZERO, BigDecimal.ZERO)

        // Find user by ID
        val userProgress = userCampaignProgressRepository.findByUserIdAndCampaignId(userId, campaignId)
            .orElse(null) ?: return Triple(0, BigDecimal.ZERO, BigDecimal.ZERO)

        // Get option orders for the user within campaign period
        val optionOrders = optionOrderRepository.findByBuyerIgnoreCaseAndStatusIn(
            userProgress.address,
            listOf(OptionStatus.EXECUTED, OptionStatus.SETTLED)
        ).filter { order ->
            order.created.isAfter(campaign.startTime) && order.created.isBefore(campaign.endTime)
        }

        val tradeCount = optionOrders.size
        val totalAmount = optionOrders.sumOf { it.bidAmount ?: it.amount }
        val totalPremium = optionOrders.sumOf { it.premiumFeeShouldPay ?: BigDecimal.ZERO }

        return Triple(tradeCount, totalAmount, totalPremium)
    }
}
