package io.vault.jasper.mapper

import io.vault.jasper.model.activity.*
import io.vault.jasper.response.CampaignResponse
import io.vault.jasper.response.CampaignRewardResponse
import io.vault.jasper.response.RewardResponse
import io.vault.jasper.response.TaskGroupResponse
import io.vault.jasper.response.TaskResponse
import io.vault.jasper.response.UserProgressResponse

/**
 * Mapper class to convert between domain models and response models
 */
object CampaignMapper {

    /**
     * Convert domain models to an CampaignResponse
     */
    fun toCampaignResponse(
        campaign: Campaign,
        tasks: List<Task>,
        taskGroups: List<TaskGroup> = listOf(),
        campaignRewards: List<CampaignReward> = listOf(),
        userProgress: UserCampaignProgress,
        rewards: List<CampaignRewardRecord>
    ): CampaignResponse {
        val taskResponses = tasks.map { task ->
            val isCompleted = userProgress.completedTaskIds.contains(task.id)
            val completionData = userProgress.taskCompletionData[task.id]

            TaskResponse(
                id = task.id!!,
                title = task.title,
                taskType = task.taskType,
                taskConfig = task.taskConfig,
                completed = isCompleted,
                completedAt = completionData?.completedAt,
                i18nInfo = task.i18nInfo
            )
        }

        val userProgressResponse = UserProgressResponse(
            completedTaskCount = userProgress.completedTaskIds.size,
            totalTaskCount = tasks.size,
            rewardsClaimed = userProgress.rewardsClaimed,
            optionTradeCount = userProgress.optionTradeCount,
            optionTradeAmount = userProgress.optionTradeAmount.stripTrailingZeros().toPlainString(),
            optionTradePremium = userProgress.optionTradePremium.stripTrailingZeros().toPlainString(),
            extraInfo = userProgress.extraInfo
        )

        val rewardResponses = rewards.map { reward ->
            RewardResponse(
                id = reward.id!!,
                rewardType = reward.rewardType,
                amount = reward.amount,
                status = reward.status,
                transactionHash = reward.transactionHash,
                created = reward.created,
                updated = reward.updated
            )
        }

        // Map task groups to task group responses
        val taskGroupResponses = taskGroups.map { taskGroup ->
            // Create a map for quick task lookup
            val taskMap = tasks.associateBy { it.id }

            var isTaskGroupCompleted = true

            // Create task responses in the order specified by taskIds
            val groupTaskResponses = taskGroup.taskIds.mapNotNull { taskId ->
                val task = taskMap[taskId]
                if (task != null) {
                    val isCompleted = userProgress.completedTaskIds.contains(task.id)
                    val completionData = userProgress.taskCompletionData[task.id]

                    if(!isCompleted){
                        isTaskGroupCompleted = false
                    }

                    TaskResponse(
                        id = task.id!!,
                        title = task.title,
                        taskType = task.taskType,
                        taskConfig = task.taskConfig,
                        completed = isCompleted,
                        completedAt = completionData?.completedAt,
                        i18nInfo = task.i18nInfo
                    )
                } else {
                    null
                }
            }

            TaskGroupResponse(
                id = taskGroup.id!!,
                title = taskGroup.title,
                taskIds = taskGroup.taskIds,
                tasks = groupTaskResponses,
                completed = isTaskGroupCompleted,
                i18nInfo = taskGroup.i18nInfo
            )
        }

        // Map campaign rewards to campaign reward responses
        val campaignRewardResponses = campaignRewards.map { campaignReward ->
            CampaignRewardResponse(
                id = campaignReward.id!!,
                campaignId = campaignReward.campaignId,
                rewardType = campaignReward.rewardType,
                rewardConfig = campaignReward.rewardConfig,
                totalRewardAmount = campaignReward.totalRewardAmount,
                distributedAmount = campaignReward.distributedAmount,
                i18nInfo = campaignReward.i18nInfo
            )
        }

        return CampaignResponse(
            id = campaign.id!!,
            name = campaign.name,
            chain = campaign.chain,
            startTime = campaign.startTime,
            endTime = campaign.endTime,
            totalRewardAmount = campaign.totalRewardAmount,
            participantCount = campaign.participantCount,
            completedCount = campaign.completedCount,
            taskGroups = taskGroupResponses,
            campaignRewards = campaignRewardResponses,
            userProgress = userProgressResponse,
            userRewards = rewardResponses,
            i18nInfo = campaign.i18nInfo
        )
    }
}
