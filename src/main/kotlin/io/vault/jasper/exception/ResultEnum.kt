package io.vault.jasper.exception

enum class ResultEnum(val code: Int, val message: String) {
    INTERNAL_SERVER_ERROR(500, "Internal Server Error"),
    BAD_REQUEST(400, "Bad Request"),
    NOT_FOUND(404, "Not Found"),
    LOGIN_FIRST(401, "Please login first"),
    LOGIN_FAILED(402, "Login failed"),
    INVALID_AUTHORIZATION_CODE(403, "Authorization code expired or incorrect"),
    SERVICE_NOT_AVAILABLE(503, "Service not available"),
    INVALID_SIGNATURE(504, "Invalid signature"),

    UNSUPPORTED_BID_ASSET(1001, "Unsupported bid asset"),
    BID_ASSET_NOT_FOUND(1002, "Bid asset not found"),
    FAILED_TO_OBTAIN_OPTION_FEES(1003, "Failed to obtain option fees"),
    // 授权失败
    AUTHORIZATION_FAILED(1004, "Authorization failed"),

    /**
     * 挂单
     */
    PENDING_ORDER_NOT_FOUND(2100, "Pending order not found"),

    /**
     * 期权订单
     */
    ORDER_NOT_FOUND(3100, "Order not found"),
    ORDER_ALREADY_EXISTS(3101, "Order already exists"),
    ORDER_TYPE_NOT_FOUND(3102, "Order type not found"),
    UNDERLYING_ASSET_NOT_FOUND(3103, "Underlying asset not found"),
    STRIKE_ASSET_NOT_FOUND(3104, "Strike asset not found"),
    PREMIUM_ASSET_NOT_FOUND(3105, "Premium asset not found"),
    BID_AMOUNT_NOT_FOUND(3106, "Bid amount not found"),
    SETTLEMENT_TIME_EXPIRED(3107, "Settlement time expired"),

    /**
     * 杂项
     */
    CHAIN_NOT_FOUND(4100, "Chain not found"),
    DECIMAL_NOT_FOUND(4101, "Decimal not found"),
    CONTRACT_NOT_FOUND(4102, "Contract not found"),
    CHAIN_NOT_SUPPORTED(4103, "Chain not supported"),
    INVALID_ADDRESS(4104, "Invalid address"),

    /**
     * 请求区块链节点时的异常
     */
    GET_TRANSACTION_FAILED(10100, "Get transaction failed"),
    TRANSACTION_FAILED(10101, "Transaction failed"),
    TRANSACTION_NOT_CONFIRMED(10102, "Transaction not confirmed"),
    TRANSACTION_RECEIPT_FAILED(10103, "Transaction receipt failed"),
    MUST_HAS_OP_SENDER(10104, "OP Sender Can not be null"),
    CAN_NOT_DECODE_OP_CALL_DATA(10105, "Can not op call data from transaction"),
    GET_BLOCK_FAILED(10106, "Get block failed"),
    TOKEN_DECIMALS_ERROR(10107, "Token decimals error"),

    /**
     * 用户
     */
    USER_NOT_FOUND(20100, "User not found"),
    INVITOR_NOT_FOUND(20101, "Invitor not found"),
    USER_ALREADY_INVITED(20102, "User already invited"),
    INVITE_CODE_NOT_FOUND(20103, "Invite code not found"),
    INVITOR_CAN_NOT_BE_YOURSELF(20104, "Invitor can not be yourself"),
    INVITOR_MUST_OLDER(20105, "The invitor must register earlier than you"),
    INVALID_LP_ACTIVATE_CODE(20106, "Invalid LP activate code"),
    LP_VAULT_ALREADY_CREATED(20107, "LP vault already created"),
    LP_VAULT_NOT_FOUND(20108, "LP vault not found"),
    LP_VAULT_STILL_CREATING(20109, "LP vault still creating"),
    DISCORD_USER_ALREADY_BOUND(20110, "Discord user already bound"),
    TWITTER_NOT_BIND(20111, "Twitter not bind"),
    INVITE_CODE_NOT_VALID(20103, "Invite code not valid"),

    /**
     * KOL
     */
    KOL_NOT_FOUND(30100, "KOL not found"),
    KOL_APPLY_IS_UNDER_REVIEW(30101, "KOL apply is under review"),
    YOU_ARE_ALREADY_A_KOL(30102, "You are already a KOL"),
    INVALID_TWITTER_HANDLE(30103, "Invalid Twitter handle"),
    INVALID_WALLET_ADDRESS(30104, "Invalid wallet address"),
    INVALID_CAMPAIGN(30105, "Invalid campaign"),
    INVALID_FROM_CHANNEL(30106, "Invalid channel"),

    /**
     * FreePremiumBenefit
     */
    USER_NO_FREE_PREMIUM_RIGHT(40100, "User has no free premium right"),
    // 用户已使用完所有权益
    USER_NO_FREE_PREMIUM_BENEFIT_LEFT(40101, "User has no free premium benefit left"),
    ACTIVITY_NOT_FOUND(40102, "Activity not found"),
    ACTIVITY_EXPIRED(40103, "Activity expired"),
    BENEFIT_NOT_FOUND(40104, "Benefit not found"),

    /**
     * 月光宝盒 & 宝石
     */
    MOONLIGHT_BOX_ALREADY_USED(50100, "Moonlight box already used"),
    // 行使权利超时
    MOONLIGHT_BOX_EXPIRED(50101, "Moonlight box expired"),
    VISIT_GALA_THREE_FIRST(50102, "Please visit gala 3 activity page first"),
    STONE_RECORD_NOT_FOUND(50103, "Stone record not found"),
    STONE_RECORD_ADDRESS_NOT_MATCH(50104, "Stone record address not match"),
    STONE_RECORD_STATUS_NOT_CREATED(50105, "Stone record status not created"),
    NOT_TIME_STONE_ORDER(50106, "Not time stone order"),

    /**
     * User Network
     */
    CAN_NOT_GEN_INVITE_CODE(60100, "Can not generate invite code"),
    INVALID_PROTOCOL_FEE_PERCENTAGE(60101, "Invalid protocol fee percentage"),
    USER_NETWORK_NOT_FOUND(60102, "User network not found"),
    PERCENTAGE_TOO_LARGE(60103, "Percentage too large"),
    CAN_NOT_BIND_YOURSELF(60104, "Can not bind yourself"),
    INVALID_INVITE_CODE(60105, "Invalid invite code"),
    USER_NETWORK_ALREADY_EXIST(60106, "User network already exist"),
    USER_ALREADY_EXIST(60107, "User already exist"),
    NOT_AUTHORIZE_TO_SET_PERCENTAGE(60108, "Not authorize to set percentage"),
    USER_NETWORK_REBATE_RECORD_NOT_FOUND(60109, "User network rebate record not found"),
    NOT_ALLOW_TO_VIEW_INFO(60110, "Not Allow To View Info"),
    INVITE_CODE_ALREADY_BIND(60111, "Invite code already bind"),
    NOT_ALLOW_TO_SET_ALIAS(60112, "Not Allow To Set Alias"),
    NOT_ALLOW_TO_SET_PERCENTAGE(60112, "Not Allow To Set Percentage"),

    /**
     * Btr Campaign
     */
    BTR_CAMPAIGN_NOT_FOUND(70100, "BTR campaign not found"),

    /**
     * Mini Bridge Swap Order
     */
    INVALID_MINI_BRIDGE_ORDER_TO_ADDRESS(80100, "To Address should be your own address or your AA Vault Address"),
    INVALID_MINI_BRIDGE_ORDER_CHAIN(80101, "Only support Bitlayer <-> Arbitrum right now"),
    INVALID_MINI_BRIDGE_ORDER_BITLAYER_ASSET(80102, "Only support BTC or USDT on Bitlayer right now"),
    INVALID_MINI_BRIDGE_ORDER_ARBITRUM_ASSET(80103, "Only support USDT on Arbitrum right now"),
    INVALID_PRICE_ORACLE(80104, "Invalid price oracle"),
    PRICE_ORACLE_NOT_FOUND(80105, "Price oracle not found"),
    INVALID_MINI_BRIDGE_ORDER_AMOUNT(80106, "Invalid mini bridge order amount"),
    MINI_BRIDGE_ORDER_NOT_FOUND(80107, "Mini bridge order not found"),
    MINI_BRIDGE_ORDER_DEPOSIT_TX_FAILED(80108, "Mini bridge order deposit tx failed"),
    MINI_BRIDGE_ORDER_DEPOSIT_TX_INVALID_FROM_ADDRESS(80109, "Mini bridge order deposit tx invalid from address"),
    MINI_BRIDGE_ORDER_DEPOSIT_TX_INVALID_TO_ADDRESS(80110, "Mini bridge order deposit tx invalid to address"),
    MINI_BRIDGE_ORDER_DEPOSIT_TX_INVALID_VALUE(80111, "Mini bridge order deposit tx invalid value"),
    MINI_BRIDGE_ORDER_HASH_ALREADY_EXISTS(80112, "Mini bridge order hash already exists"),

    /**
     * Campaign
     */
    TASK_PARAM_ERROR(90100, "Task param error"),
    TASK_NOT_FOUND(90101, "Task not found"),
    CAMPAIGN_NOT_FOUND(90102, "Campaign not found"),
    REWARD_TYPE_ALREADY_EXIST(90103, "Reward type already exists for this campaign"),
    TASK_ID_NOT_EXIST(90104, "Task id not exist"),
    CAMPAIGN_NOT_ACTIVE(90105, "Campaign not active"),
    TASK_NOT_PART_OF_CAMPAIGN(90106, "Task not part of campaign"),
    TASK_NOT_COMPLETED(90107, "Task not completed"),
    TASK_GROUP_NOT_FOUND(90108, "Task group not found"),
    TASK_ALREADY_IN_TASK_GROUP(90109, "Task already in task group"),
    TASK_NOT_IN_TASK_GROUP(90110, "Task not in task group"),
    CAMPAIGN_RANKING_NOT_FOUND(90111, "Campaign ranking not found"),
    CAMPAIGN_RANKING_ALREADY_EXISTS(90112, "Campaign ranking already exists"),
}