package io.vault.jasper.controller.pub

import io.swagger.annotations.Api
import io.swagger.annotations.ApiOperation
import io.swagger.annotations.ApiParam
import io.vault.jasper.ApiResponse
import io.vault.jasper.annotation.EVMAddress
import io.vault.jasper.exception.BusinessException
import io.vault.jasper.exception.ResultEnum
import io.vault.jasper.repository.UserRepository
import io.vault.jasper.response.CampaignRankingResponse
import io.vault.jasper.response.PageResponse
import io.vault.jasper.service.activity.CampaignRankingService
import io.vault.jasper.service.activity.CampaignService
import io.vault.jasper.utils.AuthUtil
import org.springframework.web.bind.annotation.*
import javax.servlet.http.HttpServletRequest
import javax.validation.constraints.Max
import javax.validation.constraints.Min

@RestController
@RequestMapping("/api/campaigns")
@Api(tags = ["Campaign Ranking API"])
class CampaignRankingController(
    private val campaignRankingService: CampaignRankingService,
    private val campaignService: CampaignService,
    private val authUtil: AuthUtil,
    private val userRepository: UserRepository
) {

    @GetMapping("/{campaignId}/ranking")
    @ApiOperation("Get campaign ranking leaderboard")
    @CrossOrigin
    fun getCampaignRanking(
        @PathVariable campaignId: String,
        @RequestParam(defaultValue = "0") @Min(0) page: Int,
        @RequestParam(defaultValue = "20") @Min(1) @Max(100) size: Int,
        @RequestParam(required = false) @EVMAddress address: String?,
        request: HttpServletRequest
    ): ApiResponse<CampaignRankingResponse> {
        
        authUtil.filterIPBlacklist(request)

        // Validate campaign exists
        val campaign = campaignService.getCampaignById(campaignId)
            ?: throw BusinessException(ResultEnum.CAMPAIGN_NOT_FOUND)

        // Get ranking configuration
        val campaignRanking = campaignRankingService.getCampaignRankingByCampaignId(campaignId)
            ?: throw BusinessException(ResultEnum.CAMPAIGN_RANKING_NOT_FOUND)

        // Get leaderboard data
        val leaderboardPage = campaignRankingService.getCampaignRankingLeaderboard(campaignId, page, size)

        // Get user's rank if address is provided
        var userRank: Long? = null
        var userProgress: Any? = null
        
        if (!address.isNullOrBlank()) {
            val user = userRepository.findByAddressIgnoreCase(address)
            if (user != null) {
                userRank = campaignRankingService.getUserRankInCampaign(campaignId, user.id!!)
                userProgress = campaignService.getUserCampaignProgress(user.id, user.address, campaignId)
            }
        }

        // Convert to response format
        val leaderboardData = leaderboardPage.content.mapIndexed { index, progress ->
            mapOf(
                "rank" to (page * size + index + 1),
                "address" to formatAddress(progress.address, campaignRanking.displayConfig.addressDisplayFormat),
                "value" to formatValue(progress, campaignRanking),
                "optionTradeCount" to progress.optionTradeCount,
                "optionTradeAmount" to progress.optionTradeAmount.stripTrailingZeros().toPlainString(),
                "optionTradePremium" to progress.optionTradePremium.stripTrailingZeros().toPlainString(),
                "completedTaskCount" to progress.completedTaskIds.size
            )
        }

        val pageResponse = PageResponse(
            data = leaderboardData,
            total = leaderboardPage.totalElements,
            totalPage = leaderboardPage.totalPages,
            page = leaderboardPage.number,
            pageSize = leaderboardPage.size,
            hasNext = leaderboardPage.hasNext()
        )

        val response = CampaignRankingResponse(
            campaignId = campaignId,
            rankingType = campaignRanking.rankingType,
            displayConfig = campaignRanking.displayConfig,
            leaderboard = pageResponse,
            userRank = userRank,
            userProgress = userProgress,
            i18nInfo = campaignRanking.i18nInfo
        )

        return ApiResponse.success(response)
    }

    @GetMapping("/{campaignId}/ranking/user")
    @ApiOperation("Get user's ranking in campaign")
    @CrossOrigin
    fun getUserRanking(
        @PathVariable campaignId: String,
        @RequestParam @EVMAddress address: String,
        request: HttpServletRequest
    ): ApiResponse<Map<String, Any?>> {
        
        authUtil.filterIPBlacklist(request)
        val user = userRepository.findByAddressIgnoreCase(address)
            ?: throw BusinessException(ResultEnum.USER_NOT_FOUND)

        // Validate campaign exists
        val campaign = campaignService.getCampaignById(campaignId)
            ?: throw BusinessException(ResultEnum.CAMPAIGN_NOT_FOUND)

        // Get user's rank
        val userRank = campaignRankingService.getUserRankInCampaign(campaignId, user.id!!)
        val userProgress = campaignService.getUserCampaignProgress(user.id, user.address, campaignId)

        val response = mapOf(
            "campaignId" to campaignId,
            "address" to address,
            "rank" to userRank,
            "optionTradeCount" to userProgress.optionTradeCount,
            "optionTradeAmount" to userProgress.optionTradeAmount.stripTrailingZeros().toPlainString(),
            "optionTradePremium" to userProgress.optionTradePremium.stripTrailingZeros().toPlainString(),
            "completedTaskCount" to userProgress.completedTaskIds.size
        )

        return ApiResponse.success(response)
    }

    private fun formatAddress(address: String, format: io.vault.jasper.model.activity.AddressDisplayFormat): String {
        return when (format) {
            io.vault.jasper.model.activity.AddressDisplayFormat.FULL -> address
            io.vault.jasper.model.activity.AddressDisplayFormat.MASKED -> {
                if (address.length > 8) {
                    "${address.substring(0, 6)}...${address.substring(address.length - 4)}"
                } else {
                    address
                }
            }
            io.vault.jasper.model.activity.AddressDisplayFormat.FIRST_LAST_4 -> {
                if (address.length > 8) {
                    "${address.substring(0, 4)}...${address.substring(address.length - 4)}"
                } else {
                    address
                }
            }
        }
    }

    private fun formatValue(
        progress: io.vault.jasper.model.activity.UserCampaignProgress,
        ranking: io.vault.jasper.model.activity.CampaignRanking
    ): String {
        return when (ranking.rankingType) {
            io.vault.jasper.model.activity.CampaignRankingType.OPTION_TRADE_COUNT -> progress.optionTradeCount.toString()
            io.vault.jasper.model.activity.CampaignRankingType.OPTION_TRADE_AMOUNT -> progress.optionTradeAmount.stripTrailingZeros().toPlainString()
            io.vault.jasper.model.activity.CampaignRankingType.OPTION_TRADE_PREMIUM -> progress.optionTradePremium.stripTrailingZeros().toPlainString()
            io.vault.jasper.model.activity.CampaignRankingType.COMPLETED_TASK_COUNT -> progress.completedTaskIds.size.toString()
            io.vault.jasper.model.activity.CampaignRankingType.CUSTOM_SCORE -> "0" // TODO: Calculate custom score
        }
    }
}
