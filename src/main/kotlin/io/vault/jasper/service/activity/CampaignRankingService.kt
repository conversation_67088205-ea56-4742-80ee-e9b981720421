package io.vault.jasper.service.activity

import io.vault.jasper.exception.BusinessException
import io.vault.jasper.exception.ResultEnum
import io.vault.jasper.model.activity.*
import io.vault.jasper.repository.activity.CampaignRankingRepository
import io.vault.jasper.repository.activity.UserCampaignProgressRepository
import org.slf4j.LoggerFactory
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Pageable
import org.springframework.data.repository.findByIdOrNull
import org.springframework.stereotype.Service
import java.math.BigDecimal
import java.math.RoundingMode

@Service
class CampaignRankingService(
    private val campaignRankingRepository: CampaignRankingRepository,
    private val userCampaignProgressRepository: UserCampaignProgressRepository,
    private val campaignService: CampaignService
) {
    private val logger = LoggerFactory.getLogger(this::class.java)

    /**
     * Create a new campaign ranking configuration
     */
    fun createCampaignRanking(campaignRanking: CampaignRanking): CampaignRanking {
        // Validate that the campaign exists
        val campaign = campaignService.getCampaignById(campaignRanking.campaignId)
            ?: throw BusinessException(ResultEnum.CAMPAIGN_NOT_FOUND)

        // Check if a ranking configuration already exists for this campaign
        campaignRankingRepository.findByCampaignId(campaignRanking.campaignId)
            .ifPresent {
                throw BusinessException(ResultEnum.CAMPAIGN_RANKING_ALREADY_EXISTS)
            }

        return campaignRankingRepository.save(campaignRanking)
    }

    /**
     * Get campaign ranking configuration by campaign ID
     */
    fun getCampaignRankingByCampaignId(campaignId: String): CampaignRanking? {
        return campaignRankingRepository.findByCampaignIdAndEnabled(campaignId, true).orElse(null)
    }

    /**
     * Get campaign ranking by ID
     */
    fun getCampaignRankingById(id: String): CampaignRanking? {
        return campaignRankingRepository.findByIdOrNull(id)
    }

    /**
     * Get campaign ranking leaderboard with pagination
     */
    fun getCampaignRankingLeaderboard(
        campaignId: String,
        page: Int = 0,
        size: Int = 20
    ): Page<UserCampaignProgress> {
        val campaignRanking = getCampaignRankingByCampaignId(campaignId)
            ?: throw BusinessException(ResultEnum.CAMPAIGN_RANKING_NOT_FOUND)

        val pageable: Pageable = PageRequest.of(page, size)

        return when (campaignRanking.rankingType) {
            CampaignRankingType.OPTION_TRADE_COUNT -> {
                userCampaignProgressRepository.findByCampaignIdOrderByOptionTradeCountDesc(campaignId, pageable)
            }
            CampaignRankingType.OPTION_TRADE_AMOUNT -> {
                userCampaignProgressRepository.findByCampaignIdOrderByOptionTradeAmountDesc(campaignId, pageable)
            }
            CampaignRankingType.OPTION_TRADE_PREMIUM -> {
                userCampaignProgressRepository.findByCampaignIdOrderByOptionTradePremiumDesc(campaignId, pageable)
            }
            CampaignRankingType.COMPLETED_TASK_COUNT -> {
                // For task count, we need to sort by the size of completedTaskIds
                // This requires custom implementation or aggregation
                getTaskCountRanking(campaignId, pageable)
            }
            CampaignRankingType.CUSTOM_SCORE -> {
                // For custom score, we need to calculate scores based on configuration
                getCustomScoreRanking(campaignId, campaignRanking, pageable)
            }
        }
    }

    /**
     * Get user's rank in campaign
     */
    fun getUserRankInCampaign(campaignId: String, userId: String): Long? {
        val campaignRanking = getCampaignRankingByCampaignId(campaignId) ?: return null
        val userProgress = userCampaignProgressRepository.findByUserIdAndCampaignId(userId, campaignId)
            .orElse(null) ?: return null

        return when (campaignRanking.rankingType) {
            CampaignRankingType.OPTION_TRADE_COUNT -> {
                userCampaignProgressRepository.countByCampaignIdAndOptionTradeCountGreaterThan(
                    campaignId, userProgress.optionTradeCount
                ) + 1
            }
            CampaignRankingType.OPTION_TRADE_AMOUNT -> {
                userCampaignProgressRepository.countByCampaignIdAndOptionTradeAmountGreaterThan(
                    campaignId, userProgress.optionTradeAmount
                ) + 1
            }
            CampaignRankingType.OPTION_TRADE_PREMIUM -> {
                userCampaignProgressRepository.countByCampaignIdAndOptionTradePremiumGreaterThan(
                    campaignId, userProgress.optionTradePremium
                ) + 1
            }
            CampaignRankingType.COMPLETED_TASK_COUNT -> {
                // Custom implementation needed for task count ranking
                getTaskCountUserRank(campaignId, userProgress.completedTaskIds.size)
            }
            CampaignRankingType.CUSTOM_SCORE -> {
                // Custom implementation needed for custom score ranking
                getCustomScoreUserRank(campaignId, campaignRanking, userProgress)
            }
        }
    }

    /**
     * Update campaign ranking configuration
     */
    fun updateCampaignRanking(campaignRanking: CampaignRanking): CampaignRanking {
        val existing = getCampaignRankingById(campaignRanking.id!!)
            ?: throw BusinessException(ResultEnum.CAMPAIGN_RANKING_NOT_FOUND)

        return campaignRankingRepository.save(campaignRanking)
    }

    /**
     * Calculate custom score for user progress
     */
    private fun calculateCustomScore(
        userProgress: UserCampaignProgress,
        config: Map<String, Any>
    ): BigDecimal {
        val weightTradeCount = (config[CampaignRankingConfigParams.WEIGHT_TRADE_COUNT] as? Number)?.toDouble() ?: 1.0
        val weightTradeAmount = (config[CampaignRankingConfigParams.WEIGHT_TRADE_AMOUNT] as? Number)?.toDouble() ?: 1.0
        val weightTradePremium = (config[CampaignRankingConfigParams.WEIGHT_TRADE_PREMIUM] as? Number)?.toDouble() ?: 1.0
        val weightCompletedTasks = (config[CampaignRankingConfigParams.WEIGHT_COMPLETED_TASKS] as? Number)?.toDouble() ?: 1.0

        val tradeCountScore = BigDecimal(userProgress.optionTradeCount * weightTradeCount)
        val tradeAmountScore = userProgress.optionTradeAmount.multiply(BigDecimal(weightTradeAmount))
        val tradePremiumScore = userProgress.optionTradePremium.multiply(BigDecimal(weightTradePremium))
        val taskCountScore = BigDecimal(userProgress.completedTaskIds.size * weightCompletedTasks)

        return tradeCountScore
            .add(tradeAmountScore)
            .add(tradePremiumScore)
            .add(taskCountScore)
            .setScale(2, RoundingMode.HALF_UP)
    }

    // Placeholder methods for complex ranking logic
    private fun getTaskCountRanking(campaignId: String, pageable: Pageable): Page<UserCampaignProgress> {
        // This would require MongoDB aggregation to sort by completedTaskIds size
        // For now, return basic pagination
        return userCampaignProgressRepository.findByCampaignId(campaignId, pageable)
    }

    private fun getCustomScoreRanking(
        campaignId: String,
        campaignRanking: CampaignRanking,
        pageable: Pageable
    ): Page<UserCampaignProgress> {
        // This would require calculating custom scores and sorting
        // For now, return basic pagination
        return userCampaignProgressRepository.findByCampaignId(campaignId, pageable)
    }

    private fun getTaskCountUserRank(campaignId: String, completedTaskCount: Int): Long {
        // This would require counting users with more completed tasks
        // For now, return 1
        return 1L
    }

    private fun getCustomScoreUserRank(
        campaignId: String,
        campaignRanking: CampaignRanking,
        userProgress: UserCampaignProgress
    ): Long {
        // This would require calculating user's custom score and comparing
        // For now, return 1
        return 1L
    }
}
