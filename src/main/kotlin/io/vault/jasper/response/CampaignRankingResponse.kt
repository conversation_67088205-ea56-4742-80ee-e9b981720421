package io.vault.jasper.response

import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty
import io.vault.jasper.model.activity.CampaignRankingDisplayConfig
import io.vault.jasper.model.activity.CampaignRankingType
import io.vault.jasper.model.activity.I18nCampaignRankingInfo

/**
 * Response class for campaign ranking information
 */
@ApiModel
data class CampaignRankingResponse(
    @ApiModelProperty("Campaign ID", required = true)
    val campaignId: String,

    @ApiModelProperty("Ranking type", required = true)
    val rankingType: CampaignRankingType,

    @ApiModelProperty("Display configuration", required = true)
    val displayConfig: CampaignRankingDisplayConfig,

    @ApiModelProperty("Leaderboard data with pagination", required = true)
    val leaderboard: PageResponse<Map<String, Any>>,

    @ApiModelProperty("User's rank in the campaign", required = false)
    val userRank: Long?,

    @ApiModelProperty("User's progress data", required = false)
    val userProgress: Any?,

    @ApiModelProperty("i18n info", required = false)
    val i18nInfo: Map<String, I18nCampaignRankingInfo>? = null
)
