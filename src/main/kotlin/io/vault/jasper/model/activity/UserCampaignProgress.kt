package io.vault.jasper.model.activity

import org.springframework.data.annotation.CreatedDate
import org.springframework.data.annotation.Id
import org.springframework.data.annotation.LastModifiedDate
import org.springframework.data.mongodb.core.index.CompoundIndex
import org.springframework.data.mongodb.core.index.Indexed
import org.springframework.data.mongodb.core.mapping.Document
import org.springframework.data.mongodb.core.mapping.Field
import org.springframework.data.mongodb.core.mapping.FieldType
import java.math.BigDecimal
import java.time.LocalDateTime

/**
 * UserCampaignProgress model to track user progress in campaigns
 */
@Document("user_campaign_progress")
@CompoundIndex(name = "user_campaign_idx", def = "{'user_id': 1, 'campaign_id': 1}", unique = true)
data class UserCampaignProgress(
    @Id
    val id: String? = null,

    @Field("user_id")
    @Indexed(background = true)
    val userId: String,

    @Indexed(background = true)
    val address: String,

    @Field("campaign_id")
    @Indexed(background = true)
    val campaignId: String,

    @Field("completed_task_ids")
    val completedTaskIds: MutableSet<String> = mutableSetOf(),

    @Field("task_completion_data")
    val taskCompletionData: MutableMap<String, TaskCompletionData> = mutableMapOf(),

    @Field("rewards_claimed")
    var rewardsClaimed: Boolean = false,

    @Field("option_trade_count")
    var optionTradeCount: Int = 0,

    @Field("option_trade_amount", targetType = FieldType.DECIMAL128)
    var optionTradeAmount: BigDecimal = BigDecimal.ZERO,

    @Field("option_trade_premium", targetType = FieldType.DECIMAL128)
    var optionTradePremium: BigDecimal = BigDecimal.ZERO,

    @Field("extra_info")
    var extraInfo: MutableMap<String, Any> = mutableMapOf(),

    @CreatedDate
    val created: LocalDateTime = LocalDateTime.now(),

    @LastModifiedDate
    val updated: LocalDateTime = LocalDateTime.now()
)

/**
 * Data class to store details about task completion
 */
data class TaskCompletionData(
    @Field("completed_at")
    val completedAt: LocalDateTime = LocalDateTime.now(),
    
    @Field("completion_data")
    val completionData: Map<String, Any> = mapOf()
)
